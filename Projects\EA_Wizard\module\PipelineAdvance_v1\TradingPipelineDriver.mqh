#property strict

#include "interface/ITradingPipelineDriver.mqh"
#include "TradingEvent.mqh"
#include "TradingPipelineContainerManager.mqh"
#include "TradingPipelineRegistry.mqh"
#include "TradingPipelineExplorer.mqh"

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
#define TRADING_PIPELINE_DRIVER_NAME "TradingPipelineDriver"
#define TRADING_PIPELINE_DRIVER_TYPE "PipelineDriver"
#define DEFAULT_MAX_CONTAINERS 20
#define DEFAULT_MAX_REGISTRATIONS 100

//+------------------------------------------------------------------+
//| 交易流水線驅動器 - 單例模式                                     |
//| 負責初始化和控制主要模組                                         |
//+------------------------------------------------------------------+
class TradingPipelineDriver : public ITradingPipelineDriver
{
private:
    static TradingPipelineDriver* s_instance;           // 單例實例

    // 核心組件
    TradingPipelineContainerManager* m_manager;         // 容器管理器
    TradingPipelineRegistry* m_registry;                // 註冊器
    TradingPipelineExplorer* m_explorer;                // 探索器

    // 狀態管理
    bool m_isInitialized;                               // 是否已初始化
    string m_name;                                      // 驅動器名稱
    string m_type;                                      // 驅動器類型

    //+------------------------------------------------------------------+
    //| 私有構造函數（單例模式）                                         |
    //+------------------------------------------------------------------+
    TradingPipelineDriver()
        : m_manager(NULL),
          m_registry(NULL),
          m_explorer(NULL),
          m_isInitialized(false),
          m_name(TRADING_PIPELINE_DRIVER_NAME),
          m_type(TRADING_PIPELINE_DRIVER_TYPE)
    {
        Print("[TradingPipelineDriver] 開始初始化...");

        // 執行初始化
        if(Initialize())
        {
            m_isInitialized = true;
            Print("[TradingPipelineDriver] 初始化成功");
        }
        else
        {
            Print("[錯誤] TradingPipelineDriver 初始化失敗");
            Cleanup();
        }
    }

public:
    //+------------------------------------------------------------------+
    //| 獲取單例實例                                                     |
    //+------------------------------------------------------------------+
    static TradingPipelineDriver* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new TradingPipelineDriver();
        }
        return s_instance;
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~TradingPipelineDriver()
    {
        Print("[TradingPipelineDriver] 開始清理...");
        Cleanup();
        Print("[TradingPipelineDriver] 清理完成");
    }

    //+------------------------------------------------------------------+
    //| 釋放單例實例                                                     |
    //+------------------------------------------------------------------+
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }

    //+------------------------------------------------------------------+
    //| 初始化所有組件                                                   |
    //+------------------------------------------------------------------+
    bool Initialize()
    {
        Print("[TradingPipelineDriver] 開始組件初始化...");

        // 1. 創建容器管理器
        if(!InitializeManager())
        {
            Print("[錯誤] 容器管理器初始化失敗");
            return false;
        }

        // 2. 創建註冊器（依賴管理器）
        if(!InitializeRegistry())
        {
            Print("[錯誤] 註冊器初始化失敗");
            return false;
        }

        // 3. 創建探索器（依賴註冊器）
        if(!InitializeExplorer())
        {
            Print("[錯誤] 探索器初始化失敗");
            return false;
        }

        // 4. 創建默認容器
        if(!SetupDefaultConfiguration())
        {
            Print("[錯誤] 默認配置設置失敗");
            return false;
        }

        Print("[TradingPipelineDriver] 所有組件初始化完成");
        return true;
    }

    //+------------------------------------------------------------------+
    //| 清理所有組件                                                     |
    //+------------------------------------------------------------------+
    void Cleanup()
    {
        // 按相反順序清理組件
        if(m_explorer != NULL)
        {
            delete m_explorer;
            m_explorer = NULL;
            Print("[TradingPipelineDriver] 探索器已清理");
        }

        if(m_registry != NULL)
        {
            delete m_registry;
            m_registry = NULL;
            Print("[TradingPipelineDriver] 註冊器已清理");
        }

        if(m_manager != NULL)
        {
            delete m_manager;
            m_manager = NULL;
            Print("[TradingPipelineDriver] 容器管理器已清理");
        }

        m_isInitialized = false;
    }

    //+------------------------------------------------------------------+
    //| 訪問器方法                                                       |
    //+------------------------------------------------------------------+
    TradingPipelineContainerManager* GetManager() const
    {
        return m_manager;
    }

    TradingPipelineRegistry* GetRegistry() const
    {
        return m_registry;
    }

    TradingPipelineExplorer* GetExplorer() const
    {
        return m_explorer;
    }

    //+------------------------------------------------------------------+
    //| 狀態查詢方法                                                     |
    //+------------------------------------------------------------------+
    bool IsInitialized() const
    {
        return m_isInitialized &&
               m_manager != NULL &&
               m_registry != NULL &&
               m_explorer != NULL;
    }

    string GetName() const
    {
        return m_name;
    }

    string GetType() const
    {
        return m_type;
    }

    //+------------------------------------------------------------------+
    //| 狀態報告                                                         |
    //+------------------------------------------------------------------+
    string GetStatusReport() const
    {
        string report = "=== TradingPipelineDriver 狀態報告 ===\n";
        report += "驅動器名稱: " + m_name + "\n";
        report += "驅動器類型: " + m_type + "\n";
        report += "初始化狀態: " + (m_isInitialized ? "已初始化" : "未初始化") + "\n";
        report += "容器管理器: " + (m_manager != NULL ? "正常" : "NULL") + "\n";
        report += "註冊器: " + (m_registry != NULL ? "正常" : "NULL") + "\n";
        report += "探索器: " + (m_explorer != NULL ? "正常" : "NULL") + "\n";

        if(IsInitialized())
        {
            report += "\n=== 組件統計信息 ===\n";
            report += "容器數量: " + IntegerToString(GetTotalContainers()) + "\n";
            report += "註冊數量: " + IntegerToString(GetTotalRegistrations()) + "\n";
        }

        return report;
    }

    //+------------------------------------------------------------------+
    //| 組件統計信息                                                     |
    //+------------------------------------------------------------------+
    int GetTotalContainers() const
    {
        return (m_manager != NULL) ? m_manager.GetContainerCount() : 0;
    }

    int GetTotalRegistrations() const
    {
        return (m_registry != NULL) ? m_registry.GetTotalRegistrations() : 0;
    }

    //+------------------------------------------------------------------+
    //| 重置功能                                                         |
    //+------------------------------------------------------------------+
    bool Reset()
    {
        Print("[TradingPipelineDriver] 開始重置...");
        Cleanup();
        return Initialize();
    }

    //+------------------------------------------------------------------+
    //| 設置默認配置                                                     |
    //+------------------------------------------------------------------+
    bool SetupDefaultConfiguration()
    {
        // 移除 IsInitialized() 檢查，因為此方法在初始化過程中被調用
        // 此時 m_isInitialized 尚未設置為 true
        if(m_manager == NULL || m_registry == NULL || m_explorer == NULL)
        {
            Print("[錯誤] 核心組件未初始化，無法設置默認配置");
            return false;
        }

        Print("[TradingPipelineDriver] 開始設置默認配置...");

        // 創建默認的事件容器
        bool success = true;

        // 為每個事件創建容器
        success &= CreateDefaultStageContainer(TRADING_INIT, "初始化容器");
        success &= CreateDefaultStageContainer(TRADING_TICK, "Tick處理容器");
        success &= CreateDefaultStageContainer(TRADING_DEINIT, "清理容器");

        // 為每個階段創建對應的容器（如果需要的話）
        success &= CreateDefaultStageContainerForStage(INIT_START, "初始化開始容器");
        success &= CreateDefaultStageContainerForStage(INIT_COMPLETE, "初始化完成容器");
        success &= CreateDefaultStageContainerForStage(TICK_DATA_FEED, "數據饋送容器");

        if(success)
        {
            Print("[TradingPipelineDriver] 默認配置設置成功");
        }
        else
        {
            Print("[警告] TradingPipelineDriver 默認配置設置部分失敗");
        }

        return success;
    }

    //+------------------------------------------------------------------+
    //| 獲取組件詳細信息                                                 |
    //+------------------------------------------------------------------+
    string GetComponentInfo() const
    {
        string info = "=== TradingPipelineDriver 組件信息 ===\n";

        if(m_manager != NULL)
        {
            info += "容器管理器:\n";
            info += "  - 名稱: " + m_manager.GetName() + "\n";
            info += "  - 類型: " + m_manager.GetType() + "\n";
            info += "  - 容器數量: " + IntegerToString(m_manager.GetContainerCount()) + "\n";
            info += "  - 最大容器數: " + IntegerToString(m_manager.GetMaxContainers()) + "\n";
            info += "  - 啟用狀態: " + (m_manager.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_registry != NULL)
        {
            info += "\n註冊器:\n";
            info += "  - 名稱: " + m_registry.GetName() + "\n";
            info += "  - 類型: " + m_registry.GetType() + "\n";
            info += "  - 總註冊數: " + IntegerToString(m_registry.GetTotalRegistrations()) + "\n";
            info += "  - 最大註冊數: " + IntegerToString(m_registry.GetMaxRegistrations()) + "\n";
            info += "  - 啟用狀態: " + (m_registry.IsEnabled() ? "啟用" : "禁用") + "\n";
        }

        if(m_explorer != NULL)
        {
            info += "\n探索器:\n";
            info += "  - 名稱: " + m_explorer.GetName() + "\n";
            info += "  - 類型: " + m_explorer.GetType() + "\n";
            info += "  - 描述: " + m_explorer.GetDescription() + "\n";
            info += "  - 有效狀態: " + (m_explorer.IsValid() ? "有效" : "無效") + "\n";
        }

        return info;
    }

    //+------------------------------------------------------------------+
    //| 驗證所有組件狀態                                                 |
    //+------------------------------------------------------------------+
    bool ValidateComponents() const
    {
        bool isValid = true;

        if(m_manager == NULL)
        {
            Print("[錯誤] 容器管理器為NULL");
            isValid = false;
        }

        if(m_registry == NULL)
        {
            Print("[錯誤] 註冊器為NULL");
            isValid = false;
        }

        if(m_explorer == NULL)
        {
            Print("[錯誤] 探索器為NULL");
            isValid = false;
        }

        if(isValid && m_explorer != NULL && !m_explorer.IsValid())
        {
            Print("[警告] 探索器狀態無效");
            isValid = false;
        }

        return isValid;
    }

private:
    //+------------------------------------------------------------------+
    //| 初始化容器管理器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeManager()
    {
        m_manager = new TradingPipelineContainerManager(
            "MainContainerManager",
            "ContainerManager",
            true,  // owned
            DEFAULT_MAX_CONTAINERS
        );

        if(m_manager == NULL)
        {
            Print("[錯誤] 無法創建容器管理器");
            return false;
        }

        Print("[TradingPipelineDriver] 容器管理器創建成功");
        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化註冊器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeRegistry()
    {
        if(m_manager == NULL)
        {
            Print("[錯誤] 容器管理器為NULL，無法創建註冊器");
            return false;
        }

        m_registry = new TradingPipelineRegistry(
            m_manager,
            "MainRegistry",
            "PipelineRegistry",
            DEFAULT_MAX_REGISTRATIONS,
            true  // owned
        );

        if(m_registry == NULL)
        {
            Print("[錯誤] 無法創建註冊器");
            return false;
        }

        Print("[TradingPipelineDriver] 註冊器創建成功");
        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化探索器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeExplorer()
    {
        if(m_registry == NULL)
        {
            Print("[錯誤] 註冊器為NULL，無法創建探索器");
            return false;
        }

        m_explorer = new TradingPipelineExplorer(
            m_registry,
            "MainExplorer",
            "TradingPipelineExplorer",
            "主要交易流水線探索器"
        );

        if(m_explorer == NULL)
        {
            Print("[錯誤] 無法創建探索器");
            return false;
        }

        Print("[TradingPipelineDriver] 探索器創建成功");
        return true;
    }

    //+------------------------------------------------------------------+
    //| 創建默認階段容器                                                 |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainer(ENUM_TRADING_EVENT event, string description)
    {
        if(m_registry == NULL)
        {
            Print("[錯誤] 註冊器為NULL，無法創建默認容器");
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::EventToString(event);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            Print("[錯誤] 無法創建容器: " + containerName);
            return false;
        }

        // 註冊容器
        bool registered = m_registry.Register(event, container);
        if(!registered)
        {
            Print("[錯誤] 無法註冊容器: " + containerName);
            delete container;
            return false;
        }

        Print("[TradingPipelineDriver] 默認容器創建成功: " + containerName);
        return true;
    }

    //+------------------------------------------------------------------+
    //| 為特定階段創建默認容器                                           |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainerForStage(ENUM_TRADING_STAGE stage, string description)
    {
        if(m_registry == NULL)
        {
            Print("[錯誤] 註冊器為NULL，無法創建階段容器");
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::StageToString(stage);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            Print("[錯誤] 無法創建階段容器: " + containerName);
            return false;
        }

        // 註冊容器到階段
        bool registered = m_registry.Register(stage, container);
        if(!registered)
        {
            Print("[錯誤] 無法註冊階段容器: " + containerName);
            delete container;
            return false;
        }

        Print("[TradingPipelineDriver] 默認階段容器創建成功: " + containerName);
        return true;
    }
};

//+------------------------------------------------------------------+
//| 靜態成員初始化                                                   |
//+------------------------------------------------------------------+
TradingPipelineDriver* TradingPipelineDriver::s_instance = NULL;
